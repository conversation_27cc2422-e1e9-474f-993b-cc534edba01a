import * as THREE from 'three';

export class CityAssets {
    constructor(scene) {
        this.scene = scene;
    }
    
    createCar(x, y, z) {
        const car = new THREE.Group();
        
        // Ana gövde
        const bodyGeometry = new THREE.BoxGeometry(1.8, 0.6, 3.5);
        const colors = [0xFF0000, 0x0000FF, 0x00FF00, 0xFFFF00, 0xFF00FF, 0x00FFFF];
        const bodyMaterial = new THREE.MeshLambertMaterial({ 
            color: colors[Math.floor(Math.random() * colors.length)] 
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.3;
        body.castShadow = true;
        car.add(body);
        
        // Çatı
        const roofGeometry = new THREE.BoxGeometry(1.6, 0.4, 1.8);
        const roofMaterial = new THREE.MeshLambertMaterial({ color: bodyMaterial.color });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 0.8;
        roof.castShadow = true;
        car.add(roof);
        
        // Tekerlekler
        this.addWheels(car, 1.8, 3.5);
        
        // Farlar
        this.addHeadlights(car, 1.75);
        
        car.position.set(x, y, z);
        this.scene.add(car);
        return car;
    }
    
    createTruck(x, y, z) {
        const truck = new THREE.Group();
        
        // Kabin
        const cabinGeometry = new THREE.BoxGeometry(2, 1.2, 2);
        const cabinMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const cabin = new THREE.Mesh(cabinGeometry, cabinMaterial);
        cabin.position.set(0, 0.6, 1.5);
        cabin.castShadow = true;
        truck.add(cabin);
        
        // Kargo alanı
        const cargoGeometry = new THREE.BoxGeometry(2, 1.5, 4);
        const cargoMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const cargo = new THREE.Mesh(cargoGeometry, cargoMaterial);
        cargo.position.set(0, 0.75, -1.5);
        cargo.castShadow = true;
        truck.add(cargo);
        
        // Tekerlekler (daha büyük)
        this.addWheels(truck, 2.2, 6, 0.4);
        
        // Farlar
        this.addHeadlights(truck, 2.5);
        
        truck.position.set(x, y, z);
        this.scene.add(truck);
        return truck;
    }
    
    createBus(x, y, z) {
        const bus = new THREE.Group();
        
        // Ana gövde
        const bodyGeometry = new THREE.BoxGeometry(2.2, 1.8, 8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.9;
        body.castShadow = true;
        bus.add(body);
        
        // Pencereler
        for (let i = -3; i <= 3; i++) {
            if (i !== 0) {
                const windowGeometry = new THREE.PlaneGeometry(0.8, 0.6);
                const windowMaterial = new THREE.MeshLambertMaterial({ 
                    color: 0x87CEEB, 
                    transparent: true, 
                    opacity: 0.7 
                });
                const window = new THREE.Mesh(windowGeometry, windowMaterial);
                window.position.set(1.11, 1.2, i * 1.2);
                bus.add(window);
            }
        }
        
        // Tekerlekler
        this.addWheels(bus, 2.4, 8, 0.35);
        
        // Farlar
        this.addHeadlights(bus, 4);
        
        bus.position.set(x, y, z);
        this.scene.add(bus);
        return bus;
    }
    
    addWheels(vehicle, width, length, radius = 0.3) {
        const wheelGeometry = new THREE.CylinderGeometry(radius, radius, 0.2, 8);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        
        const wheelPositions = [
            [-width/2 - 0.1, 0, length/4],   // Sol ön
            [width/2 + 0.1, 0, length/4],    // Sağ ön
            [-width/2 - 0.1, 0, -length/4],  // Sol arka
            [width/2 + 0.1, 0, -length/4]    // Sağ arka
        ];
        
        wheelPositions.forEach(pos => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.position.set(pos[0], pos[1], pos[2]);
            wheel.rotation.z = Math.PI / 2;
            wheel.castShadow = true;
            vehicle.add(wheel);
        });
    }
    
    addHeadlights(vehicle, frontZ) {
        const headlightGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const headlightMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xFFFFFF,
            emissive: 0x444444
        });
        
        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-0.6, 0.4, frontZ);
        vehicle.add(leftHeadlight);
        
        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(0.6, 0.4, frontZ);
        vehicle.add(rightHeadlight);
    }
    
    createBusStop(x, z) {
        const busStop = new THREE.Group();
        
        // Durak direği
        const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 3);
        const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const pole = new THREE.Mesh(poleGeometry, poleMaterial);
        pole.position.y = 1.5;
        pole.castShadow = true;
        busStop.add(pole);
        
        // Durak tabelası
        const signGeometry = new THREE.PlaneGeometry(1.5, 0.8);
        const signMaterial = new THREE.MeshLambertMaterial({ color: 0x0066CC });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(0, 2.5, 0);
        busStop.add(sign);
        
        // Bekleme bankı
        const benchGeometry = new THREE.BoxGeometry(2, 0.1, 0.4);
        const benchMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const bench = new THREE.Mesh(benchGeometry, benchMaterial);
        bench.position.set(0, 0.5, -0.8);
        bench.castShadow = true;
        busStop.add(bench);
        
        busStop.position.set(x, 0, z);
        this.scene.add(busStop);
        return busStop;
    }
    
    createTrashCan(x, z) {
        const trashCan = new THREE.Group();
        
        // Ana gövde
        const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.25, 0.8, 8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.4;
        body.castShadow = true;
        trashCan.add(body);
        
        // Kapak
        const lidGeometry = new THREE.CylinderGeometry(0.32, 0.32, 0.05, 8);
        const lidMaterial = new THREE.MeshLambertMaterial({ color: 0x006400 });
        const lid = new THREE.Mesh(lidGeometry, lidMaterial);
        lid.position.y = 0.82;
        lid.castShadow = true;
        trashCan.add(lid);
        
        trashCan.position.set(x, 0, z);
        this.scene.add(trashCan);
        return trashCan;
    }
    
    createBench(x, z) {
        const bench = new THREE.Group();
        
        // Oturma yeri
        const seatGeometry = new THREE.BoxGeometry(2, 0.1, 0.4);
        const seatMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const seat = new THREE.Mesh(seatGeometry, seatMaterial);
        seat.position.y = 0.5;
        seat.castShadow = true;
        bench.add(seat);
        
        // Sırtlık
        const backGeometry = new THREE.BoxGeometry(2, 0.6, 0.05);
        const backMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const back = new THREE.Mesh(backGeometry, backMaterial);
        back.position.set(0, 0.8, -0.175);
        back.castShadow = true;
        bench.add(back);
        
        // Ayaklar
        for (let i = 0; i < 4; i++) {
            const legGeometry = new THREE.CylinderGeometry(0.03, 0.03, 0.5);
            const legMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const leg = new THREE.Mesh(legGeometry, legMaterial);
            leg.position.set(
                i < 2 ? -0.8 : 0.8,
                0.25,
                i % 2 === 0 ? 0.15 : -0.15
            );
            leg.castShadow = true;
            bench.add(leg);
        }
        
        bench.position.set(x, 0, z);
        this.scene.add(bench);
        return bench;
    }
    
    createFlag(x, y, z) {
        const flag = new THREE.Group();
        
        // Bayrak direği
        const poleGeometry = new THREE.CylinderGeometry(0.05, 0.05, 15);
        const poleMaterial = new THREE.MeshLambertMaterial({ color: 0xC0C0C0 });
        const pole = new THREE.Mesh(poleGeometry, poleMaterial);
        pole.position.y = 7.5;
        pole.castShadow = true;
        flag.add(pole);
        
        // Türk bayrağı
        const flagGeometry = new THREE.PlaneGeometry(3, 2);
        const flagMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 });
        const flagMesh = new THREE.Mesh(flagGeometry, flagMaterial);
        flagMesh.position.set(1.5, 12, 0);
        flag.add(flagMesh);
        
        // Ay yıldız (basit)
        const starGeometry = new THREE.SphereGeometry(0.2, 8, 8);
        const starMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
        const star = new THREE.Mesh(starGeometry, starMaterial);
        star.position.set(1, 12, 0.01);
        flag.add(star);
        
        flag.position.set(x, y, z);
        this.scene.add(flag);
        return flag;
    }
    
    createParking(x, y, z, width, depth) {
        // Otopark zemini
        const parkingGeometry = new THREE.PlaneGeometry(width, depth);
        const parkingMaterial = new THREE.MeshLambertMaterial({ color: 0x404040 });
        const parking = new THREE.Mesh(parkingGeometry, parkingMaterial);
        parking.rotation.x = -Math.PI / 2;
        parking.position.set(x, y + 0.005, z);
        parking.receiveShadow = true;
        this.scene.add(parking);
        
        // Park çizgileri
        for (let i = 0; i < 4; i++) {
            for (let j = 0; j < 3; j++) {
                const lineGeometry = new THREE.PlaneGeometry(2.5, 0.1);
                const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
                const line = new THREE.Mesh(lineGeometry, lineMaterial);
                line.rotation.x = -Math.PI / 2;
                line.position.set(
                    x - width/2 + (i + 0.5) * (width/4),
                    y + 0.01,
                    z - depth/2 + (j + 0.5) * (depth/3)
                );
                this.scene.add(line);
            }
        }
    }
    
    createSchoolYard(x, z) {
        // Okul bahçesi
        const yardGeometry = new THREE.PlaneGeometry(25, 20);
        const yardMaterial = new THREE.MeshLambertMaterial({ color: 0x90EE90 });
        const yard = new THREE.Mesh(yardGeometry, yardMaterial);
        yard.rotation.x = -Math.PI / 2;
        yard.position.set(x, 0.005, z);
        yard.receiveShadow = true;
        this.scene.add(yard);
        
        // Basketbol potası
        const basketGeometry = new THREE.CylinderGeometry(0.05, 0.05, 3);
        const basketMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
        const basket = new THREE.Mesh(basketGeometry, basketMaterial);
        basket.position.set(x + 10, 1.5, z);
        basket.castShadow = true;
        this.scene.add(basket);
    }
}
