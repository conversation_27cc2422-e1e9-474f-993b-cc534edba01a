import * as THREE from 'three';
import { Customer } from './Customer.js';

export class CustomerManager {
    constructor(scene, world) {
        this.scene = scene;
        this.world = world;
        this.customers = [];
        this.maxCustomers = 5;
        this.spawnTimer = 0;
        this.spawnInterval = 10000; // 10 saniye
    }
    
    init() {
        // İlk müşterileri oluştur
        this.spawnInitialCustomers();
    }
    
    spawnInitialCustomers() {
        for (let i = 0; i < 3; i++) {
            setTimeout(() => {
                this.spawnCustomer();
            }, i * 2000);
        }
    }
    
    update(deltaTime = 0.1) {
        // Yeni müşteri spawn timer (delta time ile)
        this.spawnTimer += deltaTime * 1000; // milisaniyeye çevir

        if (this.spawnTimer >= this.spawnInterval && this.customers.length < this.maxCustomers) {
            this.spawnCustomer();
            this.spawnTimer = 0;
        }

        // Müşterileri güncelle
        this.customers.forEach(customer => {
            customer.update(deltaTime);
        });
    }
    
    spawnCustomer() {
        const position = this.world.getRandomSafePosition();
        const customer = new Customer(this.scene, position);
        customer.init();
        this.customers.push(customer);
        
        console.log(`Yeni müşteri spawn oldu: ${customer.name}`);
    }
    
    getNearbyCustomer(position, radius = 3) {
        return this.customers.find(customer => {
            if (customer.isPickedUp) return false;
            const distance = position.distanceTo(customer.position);
            return distance <= radius;
        });
    }
    
    pickupCustomer(customer) {
        customer.pickup();
        console.log(`Müşteri alındı: ${customer.name}`);
    }
    
    dropoffCustomer(customer) {
        // Müşteriyi listeden çıkar
        const index = this.customers.indexOf(customer);
        if (index > -1) {
            this.customers.splice(index, 1);
        }
        
        // Sahne'den kaldır
        customer.dispose();
        
        console.log(`Müşteri bırakıldı: ${customer.name}`);
        
        // Yeni müşteri spawn et
        setTimeout(() => {
            if (this.customers.length < this.maxCustomers) {
                this.spawnCustomer();
            }
        }, 5000);
    }
    
    rejectCustomer(customer) {
        customer.reject();
        console.log(`Müşteri reddedildi: ${customer.name}`);
    }
}
