# 🚕 Akasya Durağı - Taksi Oyunu

Three.js ile yapılmış GTA tarzı 3D taksi simülasyon oyunu.

## 🎮 Oyun Hakkında

Akasya Durağı, şehrin en iyi taksi şoförü olmaya çalıştığınız eğlenceli bir 3D oyundur. Müşterileri alıp hedeflerine götürerek para kazanın ve şehrin en başarılı taksi şoförü olun!

## ✨ Özellikler

- 🎯 **3D Grafik**: Three.js ile gerçekçi 3D ortam
- 🚗 **GTA Tarzı Kamera**: <PERSON>çünc<PERSON> şahıs kamera sistemi
- 👥 **Müşteri Sistemi**: Rastgele müşteriler ve diyaloglar
- 💰 **Para Sistemi**: Müşteri taşıyarak para kazanma
- 🏙️ **Şehir Ortamı**: Binalar, yollar ve çevre detayları
- 🎭 **Diyalog Sistemi**: Müşterilerle etkileşim
- 📱 **Responsive UI**: Modern kullanıcı arayüzü

## 🎮 Kontroller

| Tuş | Fonksiyon |
|-----|-----------|
| **W, A, S, D** | Taksiyi hareket ettir |
| **Space** | El freni |
| **E** | Müşteri al/bırak |
| **H** | Yardım paneli |

## 🚀 Kurulum ve Çalıştırma

### Gereksinimler
- Node.js (v16 veya üzeri)
- Modern web tarayıcısı (Chrome, Firefox, Safari, Edge)

### Kurulum
```bash
# Projeyi klonlayın
git clone [repository-url]
cd akasya-duragi-taksi-oyunu

# Bağımlılıkları yükleyin
npm install

# Geliştirme sunucusunu başlatın
npm run dev
```

### Üretim Build'i
```bash
# Üretim için build alın
npm run build

# Build'i önizleyin
npm run preview
```

## 🎯 Nasıl Oynanır

1. **Oyunu Başlatın**: "Oyuna Başla" butonuna tıklayın
2. **Müşteri Bulun**: Sarı halka içindeki müşterilere yaklaşın
3. **Müşteri Alın**: E tuşu ile müşteriyi taksiye alın
4. **Hedef**: Müşteriyi belirtilen hedefe götürün
5. **Para Kazanın**: Müşteriyi bırakıp ücretini alın
6. **Tekrarlayın**: Daha fazla müşteri taşıyarak para biriktirin

## 🏗️ Proje Yapısı

```
akasya-duragi-taksi-oyunu/
├── index.html          # Ana HTML dosyası
├── main.js             # Oyun başlatıcı
├── style.css           # CSS stilleri
├── package.json        # NPM konfigürasyonu
└── src/
    ├── Game.js         # Ana oyun sınıfı
    ├── Player.js       # Taksi ve oyuncu kontrolü
    ├── Camera.js       # GTA tarzı kamera sistemi
    ├── World.js        # 3D dünya ve çevre
    ├── CustomerManager.js  # Müşteri yönetimi
    ├── Customer.js     # Müşteri sınıfı
    ├── DialogSystem.js # Diyalog sistemi
    └── UI.js          # Kullanıcı arayüzü
```

## 🛠️ Teknolojiler

- **Three.js** - 3D grafik ve animasyon
- **Vite** - Modern build tool
- **ES6 Modules** - Modern JavaScript
- **CSS3** - Animasyonlar ve responsive tasarım

## 🎨 Oyun Mekanikleri

### Taksi Fiziği
- Gerçekçi hareket sistemi
- Hız ve ivme kontrolü
- Sürtünme ve fren sistemi
- Dönüş mekaniği

### Müşteri Sistemi
- Rastgele müşteri spawn'ı
- Sabırsızlık sistemi
- Çeşitli hedefler
- Dinamik ücret hesaplama

### Dünya
- Prosedürel bina yerleşimi
- Yol sistemi
- Çevre objeleri (ağaçlar, lambalar)
- Gölge ve ışıklandırma

## 🐛 Bilinen Sorunlar

- Bazen müşteriler binalarla çakışabilir
- Yüksek hızlarda taksi kontrolü zorlaşabilir
- Mobil cihazlarda performans optimizasyonu gerekebilir

## 🔮 Gelecek Özellikler

- [ ] Minimap sistemi
- [ ] Ses efektleri ve müzik
- [ ] Daha fazla araç tipi
- [ ] Çoklu seviye sistemi
- [ ] Leaderboard
- [ ] Mobil kontrol desteği
- [ ] Çarpışma efektleri
- [ ] Hava durumu sistemi

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📝 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakın.

## 🙏 Teşekkürler

- **Three.js** ekibine harika 3D kütüphane için
- **Vite** ekibine hızlı build tool için
- Tüm test eden ve geri bildirim veren oyunculara

## 📞 İletişim

Sorularınız veya önerileriniz için:
- GitHub Issues kullanın
- Email: [<EMAIL>]

---

**🚕 İyi oyunlar! Akasya Durağı'nın en iyi şoförü olmaya hazır mısınız?**
