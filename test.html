<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - <PERSON><PERSON><PERSON>ğı</title>
</head>
<body>
    <h1><PERSON><PERSON><PERSON> Durağı - Taksi Oyunu Test</h1>
    <p><PERSON>yun başarıyla yüklendi mi kontrol ediliyor...</p>
    
    <script type="module">
        try {
            // Three.js import test
            import * as THREE from 'three';
            console.log('✅ Three.js başarıyla yüklendi:', THREE.REVISION);
            
            // Oyun sınıfları import test
            import { Game } from './src/Game.js';
            console.log('✅ Game sınıfı başarıyla yüklendi');
            
            import { Player } from './src/Player.js';
            console.log('✅ Player sınıfı başarıyla yüklendi');
            
            import { Camera } from './src/Camera.js';
            console.log('✅ Camera sınıfı başarıyla yüklendi');
            
            import { World } from './src/World.js';
            console.log('✅ World sınıfı başarıyla yüklendi');
            
            import { CustomerManager } from './src/CustomerManager.js';
            console.log('✅ CustomerManager sınıfı başarıyla yüklendi');
            
            import { Customer } from './src/Customer.js';
            console.log('✅ Customer sınıfı başarıyla yüklendi');
            
            import { DialogSystem } from './src/DialogSystem.js';
            console.log('✅ DialogSystem sınıfı başarıyla yüklendi');
            
            import { UI } from './src/UI.js';
            console.log('✅ UI sınıfı başarıyla yüklendi');
            
            document.body.innerHTML += '<p style="color: green;">✅ Tüm modüller başarıyla yüklendi!</p>';
            document.body.innerHTML += '<p><a href="/">Ana oyuna dön</a></p>';
            
        } catch (error) {
            console.error('❌ Hata:', error);
            document.body.innerHTML += `<p style="color: red;">❌ Hata: ${error.message}</p>`;
        }
    </script>
</body>
</html>
