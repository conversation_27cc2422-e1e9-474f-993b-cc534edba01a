import * as THREE from 'three';

export class Customer {
    constructor(scene, position) {
        this.scene = scene;
        this.position = position.clone();
        this.mesh = null;
        this.isPickedUp = false;
        this.isWaiting = true;
        
        // Müşteri özellikleri
        this.name = this.generateRandomName();
        this.destination = this.generateRandomDestination();
        this.payment = this.calculatePayment();
        this.patience = 30000; // 30 saniye
        this.waitTimer = 0;
        
        // Animasyon
        this.bobOffset = Math.random() * Math.PI * 2;
        this.bobSpeed = 0.02;
    }
    
    init() {
        this.createMesh();
        this.createIndicator();
    }
    
    createMesh() {
        // Müşteri grubu
        this.mesh = new THREE.Group();
        
        // Vücut
        const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.5);
        const bodyMaterial = new THREE.MeshLambertMaterial({ 
            color: this.getRandomColor() 
        });
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.75;
        body.castShadow = true;
        this.mesh.add(body);
        
        // Kafa
        const headGeometry = new THREE.SphereGeometry(0.25, 8, 8);
        const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB5 });
        const head = new THREE.Mesh(headGeometry, headMaterial);
        head.position.y = 1.75;
        head.castShadow = true;
        this.mesh.add(head);
        
        // Kollar
        const armGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8);
        const armMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBB5 });
        
        const leftArm = new THREE.Mesh(armGeometry, armMaterial);
        leftArm.position.set(-0.5, 1.2, 0);
        leftArm.castShadow = true;
        this.mesh.add(leftArm);
        
        const rightArm = new THREE.Mesh(armGeometry, armMaterial);
        rightArm.position.set(0.5, 1.2, 0);
        rightArm.castShadow = true;
        this.mesh.add(rightArm);
        
        // Bacaklar
        const legGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8);
        const legMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        
        const leftLeg = new THREE.Mesh(legGeometry, legMaterial);
        leftLeg.position.set(-0.2, 0.4, 0);
        leftLeg.castShadow = true;
        this.mesh.add(leftLeg);
        
        const rightLeg = new THREE.Mesh(legGeometry, legMaterial);
        rightLeg.position.set(0.2, 0.4, 0);
        rightLeg.castShadow = true;
        this.mesh.add(rightLeg);
        
        // Pozisyonu ayarla
        this.mesh.position.copy(this.position);
        this.scene.add(this.mesh);
    }
    
    createIndicator() {
        // Müşteri göstergesi (sarı daire)
        const indicatorGeometry = new THREE.RingGeometry(0.8, 1.2, 16);
        const indicatorMaterial = new THREE.MeshBasicMaterial({ 
            color: 0xFFD700,
            transparent: true,
            opacity: 0.7,
            side: THREE.DoubleSide
        });
        this.indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        this.indicator.rotation.x = -Math.PI / 2;
        this.indicator.position.set(this.position.x, 0.1, this.position.z);
        this.scene.add(this.indicator);
    }
    
    update(deltaTime = 0.1) {
        if (!this.isPickedUp && this.isWaiting) {
            // Sabırsızlık timer (delta time ile)
            this.waitTimer += deltaTime * 1000; // milisaniyeye çevir

            // Zıplama animasyonu (frame rate bağımsız)
            const time = Date.now() * 0.001; // saniyeye çevir
            const bobAmount = Math.sin(time * this.bobSpeed + this.bobOffset) * 0.1;
            this.mesh.position.y = bobAmount;

            // Gösterge animasyonu (frame rate bağımsız)
            if (this.indicator) {
                this.indicator.rotation.z += 0.02 * deltaTime * 60; // 60 FPS referans

                // Sabırsızlık göstergesi
                const patience = 1 - (this.waitTimer / this.patience);
                this.indicator.material.color.setHSL(patience * 0.33, 1, 0.5); // Sarıdan kırmızıya
            }

            // Sabır tükendiyse müşteriyi kaldır
            if (this.waitTimer >= this.patience) {
                this.leave();
            }
        }
    }
    
    pickup() {
        this.isPickedUp = true;
        this.isWaiting = false;
        
        // Göstergeyi kaldır
        if (this.indicator) {
            this.scene.remove(this.indicator);
            this.indicator = null;
        }
        
        // Müşteriyi gizle (taksiye bindi)
        this.mesh.visible = false;
    }
    
    reject() {
        // Müşteri reddedildi, kızgın animasyon
        this.isWaiting = false;
        
        // Kırmızı gösterge
        if (this.indicator) {
            this.indicator.material.color.setHex(0xFF0000);
        }
        
        // 2 saniye sonra gitsin
        setTimeout(() => {
            this.leave();
        }, 2000);
    }
    
    leave() {
        this.dispose();
    }
    
    dispose() {
        if (this.mesh) {
            this.scene.remove(this.mesh);
        }
        if (this.indicator) {
            this.scene.remove(this.indicator);
        }
    }
    
    generateRandomName() {
        const names = [
            'Ahmet Bey', 'Fatma Hanım', 'Mehmet Abi', 'Ayşe Teyze',
            'Ali Amca', 'Zeynep Hanım', 'Mustafa Bey', 'Emine Abla',
            'Hasan Usta', 'Gülsüm Hanım', 'İbrahim Dede', 'Hatice Nine',
            'Ömer Bey', 'Rukiye Hanım', 'Yusuf Abi', 'Meryem Teyze'
        ];
        return names[Math.floor(Math.random() * names.length)];
    }
    
    generateRandomDestination() {
        const destinations = [
            'Akasya AVM', 'Hastane', 'Okul', 'İş Merkezi',
            'Pazar', 'Otogar', 'Havalimanı', 'Tren Garı',
            'Belediye', 'Postane', 'Banka', 'Eczane',
            'Kafe', 'Restoran', 'Park', 'Spor Salonu'
        ];
        return destinations[Math.floor(Math.random() * destinations.length)];
    }
    
    calculatePayment() {
        // 15-50 TL arası rastgele ücret
        return Math.floor(Math.random() * 35) + 15;
    }
    
    getRandomColor() {
        const colors = [
            0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4,
            0xFECA57, 0xFF9FF3, 0x54A0FF, 0x5F27CD
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    getGreeting() {
        const greetings = [
            `Merhaba! Ben ${this.name}. ${this.destination}'a gitmem gerekiyor.`,
            `Selam şoför! ${this.destination}'a kadar beni götürür müsün?`,
            `İyi günler! ${this.destination}'a acil gitmem lazım.`,
            `Merhaba! ${this.destination}'a gidiyorum, müsait misin?`
        ];
        return greetings[Math.floor(Math.random() * greetings.length)];
    }
    
    getFarewell() {
        const farewells = [
            `Teşekkürler! İşte ${this.payment} TL.`,
            `Çok sağol şoför! Al bakalım ${this.payment} TL.`,
            `Eline sağlık! ${this.payment} TL, üstü kalsın.`,
            `Güzel sürüş oldu! ${this.payment} TL tam hesap.`
        ];
        return farewells[Math.floor(Math.random() * farewells.length)];
    }
    
    getPayment() {
        return this.payment;
    }
}
