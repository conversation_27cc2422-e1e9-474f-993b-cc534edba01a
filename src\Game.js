import * as THREE from 'three';
import { Player } from './Player.js';
import { Camera } from './Camera.js';
import { World } from './World.js';
import { CustomerManager } from './CustomerManager.js';
import { DialogSystem } from './DialogSystem.js';
import { UI } from './UI.js';

export class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.scene = null;
        this.renderer = null;
        this.camera = null;
        this.player = null;
        this.world = null;
        this.customerManager = null;
        this.dialogSystem = null;
        this.ui = null;

        // Oyun durumu
        this.isRunning = false;
        this.gameState = {
            money: 0,
            customersServed: 0,
            currentCustomer: null
        };

        // Input handling
        this.keys = {};

        // Performans optimizasyonu
        this.clock = new THREE.Clock();
        this.deltaTime = 0;
        this.frameCount = 0;
        this.lastFPSUpdate = 0;
        this.fps = 60;

        // Update intervals
        this.uiUpdateInterval = 0;
        this.customerUpdateInterval = 0;

        this.setupInputHandlers();
    }
    
    async init() {
        console.log('Oyun başlatılıyor...');
        
        // Three.js temel kurulum
        this.setupRenderer();
        this.setupScene();
        
        // Oyun bileşenlerini oluştur
        this.world = new World(this.scene);
        this.player = new Player(this.scene, this.world);
        this.camera = new Camera(this.scene);
        this.customerManager = new CustomerManager(this.scene, this.world);
        this.dialogSystem = new DialogSystem();
        this.ui = new UI(this.gameState);
        
        // Dünyayı oluştur
        await this.world.init();
        
        // Oyuncuyu başlat
        this.player.init();
        
        // Kamerayı oyuncuya bağla
        this.camera.setTarget(this.player.taxi);
        
        // Müşteri sistemini başlat
        this.customerManager.init();
        
        // UI'yi başlat
        this.ui.init();
        
        console.log('Oyun başarıyla yüklendi!');
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: window.devicePixelRatio === 1, // Sadece düşük DPI'da antialias
            powerPreference: "high-performance"
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Max 2x pixel ratio
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.setClearColor(0x87CEEB); // Gökyüzü mavisi

        // Performans optimizasyonları
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        
        // Fog ekle
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
        
        // Işıklandırma
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -50;
        directionalLight.shadow.camera.right = 50;
        directionalLight.shadow.camera.top = 50;
        directionalLight.shadow.camera.bottom = -50;
        this.scene.add(directionalLight);
    }
    
    setupInputHandlers() {
        // Klavye olayları
        document.addEventListener('keydown', (event) => {
            this.keys[event.code] = true;
            
            // Özel tuşlar
            if (event.code === 'KeyE') {
                this.handleInteraction();
            }
        });
        
        document.addEventListener('keyup', (event) => {
            this.keys[event.code] = false;
        });
    }
    
    handleInteraction() {
        // Müşteri etkileşimi
        const nearbyCustomer = this.customerManager.getNearbyCustomer(this.player.taxi.position);
        
        if (nearbyCustomer && !this.gameState.currentCustomer) {
            // Müşteri al
            this.pickupCustomer(nearbyCustomer);
        } else if (this.gameState.currentCustomer) {
            // Müşteriyi bırak
            this.dropoffCustomer();
        }
    }
    
    pickupCustomer(customer) {
        this.gameState.currentCustomer = customer;
        this.customerManager.pickupCustomer(customer);
        
        // Diyalog göster
        this.dialogSystem.showDialog(
            customer.getGreeting(),
            [
                {
                    text: "Tabii, hemen gidelim!",
                    action: () => {
                        this.ui.showCustomerInfo(customer);
                        this.dialogSystem.hideDialog();
                    }
                },
                {
                    text: "Üzgünüm, müsait değilim.",
                    action: () => {
                        this.gameState.currentCustomer = null;
                        this.customerManager.rejectCustomer(customer);
                        this.dialogSystem.hideDialog();
                    }
                }
            ]
        );
    }
    
    dropoffCustomer() {
        const customer = this.gameState.currentCustomer;
        const payment = customer.getPayment();
        
        // Para ekle
        this.gameState.money += payment;
        this.gameState.customersServed++;
        
        // Müşteriyi bırak
        this.customerManager.dropoffCustomer(customer);
        this.gameState.currentCustomer = null;
        
        // UI güncelle
        this.ui.hideCustomerInfo();
        this.ui.updateScore(this.gameState);
        
        // Teşekkür diyalogu
        this.dialogSystem.showDialog(
            customer.getFarewell(),
            [
                {
                    text: "İyi günler!",
                    action: () => this.dialogSystem.hideDialog()
                }
            ]
        );
    }
    
    start() {
        this.isRunning = true;
        this.gameLoop();
    }
    
    gameLoop() {
        if (!this.isRunning) return;

        // Delta time hesapla
        this.deltaTime = this.clock.getDelta();

        // FPS hesapla
        this.frameCount++;
        if (this.clock.elapsedTime - this.lastFPSUpdate > 1.0) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastFPSUpdate = this.clock.elapsedTime;

            // FPS göstergesi güncelle
            const fpsElement = document.getElementById('fps-counter');
            if (fpsElement) {
                fpsElement.textContent = `FPS: ${this.fps}`;
                // FPS'e göre renk değiştir
                if (this.fps >= 50) {
                    fpsElement.style.color = '#00FF00'; // Yeşil
                } else if (this.fps >= 30) {
                    fpsElement.style.color = '#FFFF00'; // Sarı
                } else {
                    fpsElement.style.color = '#FF0000'; // Kırmızı
                }
            }
        }

        // Oyun güncellemeleri
        this.update();
        this.render();

        requestAnimationFrame(() => this.gameLoop());
    }

    update() {
        // Oyuncu güncelle (her frame)
        this.player.update(this.keys, this.deltaTime);

        // Kamera güncelle (her frame)
        this.camera.update(this.deltaTime);

        // Müşteri sistemi güncelle (daha az sıklıkta)
        this.customerUpdateInterval += this.deltaTime;
        if (this.customerUpdateInterval > 0.1) { // 10 FPS
            this.customerManager.update(this.customerUpdateInterval);
            this.customerUpdateInterval = 0;
        }

        // UI güncelle (daha az sıklıkta)
        this.uiUpdateInterval += this.deltaTime;
        if (this.uiUpdateInterval > 0.05) { // 20 FPS
            this.ui.updateScore(this.gameState);
            this.uiUpdateInterval = 0;
        }
    }
    
    render() {
        this.renderer.render(this.scene, this.camera.camera);
    }
    
    onWindowResize() {
        this.camera.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    dispose() {
        this.isRunning = false;
        
        // Kaynakları temizle
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        if (this.scene) {
            this.scene.clear();
        }
    }
}
