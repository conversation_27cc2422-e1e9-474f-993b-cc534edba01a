import * as THREE from 'three';

export class Player {
    constructor(scene, world) {
        this.scene = scene;
        this.world = world;
        this.taxi = null;
        
        // Hareket değişkenleri
        this.velocity = new THREE.Vector3();
        this.acceleration = new THREE.Vector3();
        this.maxSpeed = 0.5;
        this.accelerationForce = 0.02;
        this.friction = 0.95;
        this.turnSpeed = 0.03;
        
        // Taksi özellikleri
        this.engineSound = null;
        this.isMoving = false;
    }
    
    init() {
        this.createTaxi();
        this.setupPhysics();
    }
    
    createTaxi() {
        // Taksi grubu
        this.taxi = new THREE.Group();
        
        // Ana gövde
        const bodyGeometry = new THREE.BoxGeometry(2, 0.8, 4);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 }); // Sarı taksi
        const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
        body.position.y = 0.4;
        body.castShadow = true;
        body.receiveShadow = true;
        this.taxi.add(body);
        
        // Çatı
        const roofGeometry = new THREE.BoxGeometry(1.8, 0.4, 2);
        const roofMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
        const roof = new THREE.Mesh(roofGeometry, roofMaterial);
        roof.position.y = 1;
        roof.castShadow = true;
        this.taxi.add(roof);
        
        // Taksi işareti
        const signGeometry = new THREE.BoxGeometry(1, 0.2, 0.5);
        const signMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(0, 1.3, 0);
        sign.castShadow = true;
        this.taxi.add(sign);
        
        // Tekerlekler
        this.wheels = [];
        const wheelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2, 8);
        const wheelMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        
        const wheelPositions = [
            [-0.8, 0, 1.2],   // Sol ön
            [0.8, 0, 1.2],    // Sağ ön
            [-0.8, 0, -1.2],  // Sol arka
            [0.8, 0, -1.2]    // Sağ arka
        ];
        
        wheelPositions.forEach(pos => {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.position.set(pos[0], pos[1], pos[2]);
            wheel.rotation.z = Math.PI / 2;
            wheel.castShadow = true;
            this.wheels.push(wheel);
            this.taxi.add(wheel);
        });
        
        // Farlar
        const headlightGeometry = new THREE.SphereGeometry(0.15, 8, 8);
        const headlightMaterial = new THREE.MeshLambertMaterial({ 
            color: 0xFFFFFF,
            emissive: 0x444444
        });
        
        const leftHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        leftHeadlight.position.set(-0.6, 0.5, 2.1);
        this.taxi.add(leftHeadlight);
        
        const rightHeadlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
        rightHeadlight.position.set(0.6, 0.5, 2.1);
        this.taxi.add(rightHeadlight);
        
        // Taksiyi sahneye ekle
        this.taxi.position.set(0, 0, 0);
        this.taxi.rotation.y = Math.PI; // 180 derece çevir (farlar -Z yönüne baksın)
        this.scene.add(this.taxi);
    }
    
    setupPhysics() {
        // Basit fizik sistemi
        this.taxi.userData = {
            velocity: new THREE.Vector3(),
            angularVelocity: 0
        };
    }
    
    update(keys) {
        this.handleInput(keys);
        this.updatePhysics();
        this.updateWheels();
        this.checkBoundaries();
    }
    
    handleInput(keys) {
        const forward = new THREE.Vector3(0, 0, 1); // Z ekseni pozitif yönü (taksi 180° çevrildi)
        const right = new THREE.Vector3(1, 0, 0);

        // Taksi yönünü al
        forward.applyQuaternion(this.taxi.quaternion);
        right.applyQuaternion(this.taxi.quaternion);

        this.isMoving = false;

        // İleri/Geri hareket
        if (keys['KeyW']) {
            this.acceleration.add(forward.multiplyScalar(this.accelerationForce));
            this.isMoving = true;
        }
        if (keys['KeyS']) {
            this.acceleration.add(forward.multiplyScalar(-this.accelerationForce * 0.7));
            this.isMoving = true;
        }
        
        // Dönüş (sadece hareket halindeyken)
        if (this.velocity.length() > 0.01) {
            if (keys['KeyA']) {
                this.taxi.rotation.y += this.turnSpeed;
            }
            if (keys['KeyD']) {
                this.taxi.rotation.y -= this.turnSpeed;
            }
        }
        
        // El freni
        if (keys['Space']) {
            this.velocity.multiplyScalar(0.9);
        }
    }
    
    updatePhysics() {
        // Hızı güncelle
        this.velocity.add(this.acceleration);
        
        // Maksimum hız sınırı
        if (this.velocity.length() > this.maxSpeed) {
            this.velocity.normalize().multiplyScalar(this.maxSpeed);
        }
        
        // Sürtünme
        this.velocity.multiplyScalar(this.friction);
        
        // Pozisyonu güncelle
        this.taxi.position.add(this.velocity);
        
        // Acceleration'ı sıfırla
        this.acceleration.set(0, 0, 0);
    }
    
    updateWheels() {
        // Tekerlekleri döndür
        const wheelRotation = this.velocity.length() * 10;
        this.wheels.forEach(wheel => {
            wheel.rotation.x += wheelRotation;
        });
    }
    
    checkBoundaries() {
        // Dünya sınırlarını kontrol et
        const boundary = 45;
        
        if (this.taxi.position.x > boundary) {
            this.taxi.position.x = boundary;
            this.velocity.x = 0;
        }
        if (this.taxi.position.x < -boundary) {
            this.taxi.position.x = -boundary;
            this.velocity.x = 0;
        }
        if (this.taxi.position.z > boundary) {
            this.taxi.position.z = boundary;
            this.velocity.z = 0;
        }
        if (this.taxi.position.z < -boundary) {
            this.taxi.position.z = -boundary;
            this.velocity.z = 0;
        }
        
        // Y pozisyonunu sabit tut (zemin seviyesi)
        this.taxi.position.y = 0;
    }
    
    getPosition() {
        return this.taxi.position.clone();
    }
    
    getRotation() {
        return this.taxi.rotation.y;
    }
}
