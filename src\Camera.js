import * as THREE from 'three';

export class Camera {
    constructor(scene) {
        this.scene = scene;
        this.camera = null;
        this.target = null;
        
        // Kamera ayarları
        this.distance = 15;
        this.height = 8;
        this.angle = 0.3; // <PERSON><PERSON><PERSON>dan bakış açısı
        
        // Smooth following
        this.currentPosition = new THREE.Vector3();
        this.currentLookAt = new THREE.Vector3();
        this.smoothness = 0.05;
        
        this.init();
    }
    
    init() {
        // Perspektif kamera oluştur
        this.camera = new THREE.PerspectiveCamera(
            75, // FOV
            window.innerWidth / window.innerHeight, // Aspect ratio
            0.1, // Near
            1000 // Far
        );
        
        // Başlangıç pozisyonu
        this.camera.position.set(0, this.height, this.distance);
        this.camera.lookAt(0, 0, 0);
        
        this.currentPosition.copy(this.camera.position);
        this.currentLookAt.set(0, 0, 0);
    }
    
    setTarget(target) {
        this.target = target;
    }
    
    update(deltaTime = 0.016) {
        if (!this.target) return;

        // Hedef pozisyon ve rotasyon
        const targetPosition = this.target.position.clone();
        const targetRotation = this.target.rotation.y;

        // Kamera pozisyonunu hesapla (GTA tarzı)
        // Kamera arabanın arkasında olmalı (-Z yönünde)
        const offset = new THREE.Vector3(0, this.height, -this.distance);

        // Taksi rotasyonuna göre offset'i döndür
        const rotationMatrix = new THREE.Matrix4();
        rotationMatrix.makeRotationY(targetRotation);
        offset.applyMatrix4(rotationMatrix);

        // Hedef kamera pozisyonu
        const desiredPosition = targetPosition.clone().add(offset);

        // Smooth interpolation (frame rate bağımsız)
        const smoothnessFactor = 1 - Math.pow(1 - this.smoothness, deltaTime * 60);
        this.currentPosition.lerp(desiredPosition, smoothnessFactor);
        this.currentLookAt.lerp(targetPosition, smoothnessFactor);

        // Kamerayı güncelle
        this.camera.position.copy(this.currentPosition);
        this.camera.lookAt(this.currentLookAt);
    }
    
    // Kamera modları
    setMode(mode) {
        switch(mode) {
            case 'close':
                this.distance = 10;
                this.height = 5;
                break;
            case 'far':
                this.distance = 20;
                this.height = 12;
                break;
            case 'top':
                this.distance = 5;
                this.height = 20;
                break;
            default:
                this.distance = 15;
                this.height = 8;
        }
    }
    
    // Kamera sallama efekti (çarpışma vs.)
    shake(intensity = 1, duration = 500) {
        const originalPosition = this.camera.position.clone();
        const startTime = Date.now();
        
        const shakeEffect = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < duration) {
                const progress = elapsed / duration;
                const currentIntensity = intensity * (1 - progress);
                
                this.camera.position.x = originalPosition.x + (Math.random() - 0.5) * currentIntensity;
                this.camera.position.y = originalPosition.y + (Math.random() - 0.5) * currentIntensity;
                this.camera.position.z = originalPosition.z + (Math.random() - 0.5) * currentIntensity;
                
                requestAnimationFrame(shakeEffect);
            } else {
                this.camera.position.copy(originalPosition);
            }
        };
        
        shakeEffect();
    }
}
