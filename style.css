* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #000;
    overflow: hidden;
}

#game-container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#game-canvas {
    display: block;
    width: 100%;
    height: 100%;
}

#ui-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

#score-panel {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px;
    border-radius: 10px;
    font-size: 18px;
    border: 2px solid #ffd700;
}

.score-item {
    margin-bottom: 5px;
}

#controls-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px;
    border-radius: 10px;
    font-size: 14px;
    border: 2px solid #ffd700;
}

#controls-info div {
    margin-bottom: 3px;
}

#dialog-box {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 15px;
    max-width: 500px;
    border: 3px solid #ffd700;
    pointer-events: auto;
}

#dialog-text {
    font-size: 16px;
    margin-bottom: 15px;
    line-height: 1.4;
}

#dialog-options button {
    background: #ffd700;
    color: black;
    border: none;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
}

#dialog-options button:hover {
    background: #ffed4e;
}

#customer-info {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 15px;
    border-radius: 10px;
    font-size: 16px;
    border: 2px solid #00ff00;
}

#customer-info div {
    margin-bottom: 5px;
}

#start-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    text-align: center;
    z-index: 200;
}

#start-screen h1 {
    font-size: 4em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

#start-screen h2 {
    font-size: 2em;
    margin-bottom: 20px;
    color: #ffd700;
}

#start-screen p {
    font-size: 1.2em;
    margin-bottom: 30px;
    opacity: 0.9;
}

#start-button {
    background: #ffd700;
    color: black;
    border: none;
    padding: 15px 30px;
    font-size: 1.5em;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

#start-button:hover {
    background: #ffed4e;
    transform: scale(1.05);
}

#loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 150;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #333;
    border-top: 5px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(100px);
    }
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.hidden {
    display: none !important;
}

/* Responsive */
@media (max-width: 768px) {
    #score-panel, #controls-info, #customer-info {
        font-size: 14px;
        padding: 10px;
    }
    
    #start-screen h1 {
        font-size: 2.5em;
    }
    
    #start-screen h2 {
        font-size: 1.5em;
    }
}
