<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>De<PERSON> - <PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .demo-container {
            background: rgba(0,0,0,0.7);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            border-left: 4px solid #ffd700;
        }
        .button {
            background: #ffd700;
            color: black;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #ffed4e;
            transform: scale(1.05);
        }
        .screenshot {
            width: 100%;
            max-width: 600px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .control-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .control-key {
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: monospace;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🚕 Akasya Durağı - Taksi Oyunu</h1>
        <p>Three.js ile yapılmış GTA tarzı 3D taksi simülasyon oyunu</p>
        
        <div style="text-align: center;">
            <button class="button" onclick="window.open('/', '_blank')">
                🎮 Oyunu Oyna
            </button>
            <button class="button" onclick="window.open('/test.html', '_blank')">
                🔧 Teknik Test
            </button>
        </div>
    </div>

    <div class="demo-container">
        <h2>🎯 Oyun Özellikleri</h2>
        
        <div class="feature">
            <h3>🎮 3D Grafik ve Animasyon</h3>
            <p>Three.js ile gerçekçi 3D ortam, gölgeler ve ışıklandırma</p>
        </div>
        
        <div class="feature">
            <h3>🚗 GTA Tarzı Kamera</h3>
            <p>Üçüncü şahıs kamera sistemi ile sinematik oyun deneyimi</p>
        </div>
        
        <div class="feature">
            <h3>👥 Akıllı Müşteri Sistemi</h3>
            <p>Rastgele spawn olan müşteriler, diyaloglar ve sabırsızlık sistemi</p>
        </div>
        
        <div class="feature">
            <h3>🏙️ Detaylı Şehir</h3>
            <p>Binalar, yollar, ağaçlar ve sokak lambaları ile canlı şehir</p>
        </div>
        
        <div class="feature">
            <h3>💰 Ekonomi Sistemi</h3>
            <p>Müşteri taşıyarak para kazanma ve skor sistemi</p>
        </div>
        
        <div class="feature">
            <h3>🎭 Etkileşimli Diyaloglar</h3>
            <p>Müşterilerle konuşma ve seçenek sistemi</p>
        </div>
    </div>

    <div class="demo-container">
        <h2>🎮 Kontroller</h2>
        
        <div class="controls">
            <div class="control-item">
                <div class="control-key">W A S D</div>
                <p>Taksi Hareketi</p>
            </div>
            <div class="control-item">
                <div class="control-key">SPACE</div>
                <p>El Freni</p>
            </div>
            <div class="control-item">
                <div class="control-key">E</div>
                <p>Müşteri Al/Bırak</p>
            </div>
            <div class="control-item">
                <div class="control-key">H</div>
                <p>Yardım Paneli</p>
            </div>
        </div>
    </div>

    <div class="demo-container">
        <h2>🚀 Nasıl Oynanır</h2>
        
        <ol style="font-size: 18px; line-height: 1.6;">
            <li><strong>Oyunu Başlatın:</strong> "Oyuna Başla" butonuna tıklayın</li>
            <li><strong>Müşteri Bulun:</strong> Sarı halka içindeki müşterilere yaklaşın</li>
            <li><strong>Müşteri Alın:</strong> E tuşu ile müşteriyi taksiye alın</li>
            <li><strong>Hedef:</strong> Müşteriyi belirtilen hedefe götürün</li>
            <li><strong>Para Kazanın:</strong> Müşteriyi bırakıp ücretini alın</li>
            <li><strong>Tekrarlayın:</strong> Daha fazla müşteri taşıyarak para biriktirin</li>
        </ol>
    </div>

    <div class="demo-container">
        <h2>🛠️ Teknik Detaylar</h2>
        
        <div class="feature">
            <h3>🔧 Teknolojiler</h3>
            <ul>
                <li><strong>Three.js:</strong> 3D grafik ve animasyon</li>
                <li><strong>Vite:</strong> Modern build tool</li>
                <li><strong>ES6 Modules:</strong> Modern JavaScript</li>
                <li><strong>CSS3:</strong> Animasyonlar ve responsive tasarım</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>📁 Proje Yapısı</h3>
            <ul>
                <li><strong>Game.js:</strong> Ana oyun döngüsü ve koordinasyon</li>
                <li><strong>Player.js:</strong> Taksi fiziği ve kontrolleri</li>
                <li><strong>Camera.js:</strong> GTA tarzı kamera sistemi</li>
                <li><strong>World.js:</strong> 3D dünya ve çevre oluşturma</li>
                <li><strong>CustomerManager.js:</strong> Müşteri spawn ve yönetimi</li>
                <li><strong>DialogSystem.js:</strong> Etkileşimli diyalog sistemi</li>
                <li><strong>UI.js:</strong> Kullanıcı arayüzü ve animasyonlar</li>
            </ul>
        </div>
    </div>

    <div class="demo-container">
        <h2>🎯 Gelecek Özellikler</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
            <div class="feature">
                <h4>🗺️ Minimap</h4>
                <p>Şehir haritası ve müşteri konumları</p>
            </div>
            <div class="feature">
                <h4>🔊 Ses Sistemi</h4>
                <p>Motor sesleri ve ortam müziği</p>
            </div>
            <div class="feature">
                <h4>🚗 Daha Fazla Araç</h4>
                <p>Farklı taksi modelleri</p>
            </div>
            <div class="feature">
                <h4>🏆 Leaderboard</h4>
                <p>En iyi şoförler sıralaması</p>
            </div>
            <div class="feature">
                <h4>📱 Mobil Destek</h4>
                <p>Dokunmatik kontroller</p>
            </div>
            <div class="feature">
                <h4>🌦️ Hava Durumu</h4>
                <p>Yağmur ve gece/gündüz döngüsü</p>
            </div>
        </div>
    </div>

    <div class="demo-container" style="text-align: center;">
        <h2>🚕 Oyuna Başlamaya Hazır mısınız?</h2>
        <p>Akasya Durağı'nın en iyi taksi şoförü olmak için hemen başlayın!</p>
        
        <button class="button" onclick="window.open('/', '_blank')" style="font-size: 24px; padding: 20px 40px;">
            🎮 OYUNU BAŞLAT
        </button>
    </div>

    <script>
        // Demo sayfası için basit animasyonlar
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature');
            
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateY(20px)';
                feature.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
