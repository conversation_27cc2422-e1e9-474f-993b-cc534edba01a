import * as THREE from 'three';
import { CityAssets } from './CityAssets.js';

export class World {
    constructor(scene) {
        this.scene = scene;
        this.buildings = [];
        this.roads = [];
        this.props = [];
        this.vehicles = [];

        // Şehir parametreleri
        this.citySize = 200;
        this.blockSize = 40;
        this.roadWidth = 8;

        // Performans optimizasyonu için LOD
        this.lodLevels = {
            high: 50,    // Yüksek detay mesafesi
            medium: 100, // Orta detay mesafesi
            low: 150     // Düşük detay mesafesi
        };

        // CityAssets helper
        this.cityAssets = new CityAssets(scene);
    }

    async init() {
        console.log('🏙️ Şehir oluşturuluyor...');

        this.createGround();
        this.createRoadNetwork();
        this.createCityBlocks();
        this.createLandmarks();
        this.createTraffic();
        this.createEnvironment();
        this.createSkybox();

        console.log('✅ Şehir oluşturuldu!');
    }
    
    createGround() {
        // Ana zemin - daha büyük şehir için
        const groundGeometry = new THREE.PlaneGeometry(this.citySize, this.citySize);
        const groundMaterial = new THREE.MeshLambertMaterial({
            color: 0x4A5D23 // Daha gerçekçi toprak rengi
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);

        // Çim alanları
        this.createGrassAreas();
    }

    createGrassAreas() {
        const grassPositions = [
            // Park alanları
            [60, 0, 60], [-60, 0, 60], [60, 0, -60], [-60, 0, -60],
            // Küçük yeşil alanlar
            [30, 0, 0], [-30, 0, 0], [0, 0, 30], [0, 0, -30]
        ];

        grassPositions.forEach(pos => {
            const grassGeometry = new THREE.PlaneGeometry(20, 20);
            const grassMaterial = new THREE.MeshLambertMaterial({
                color: 0x90EE90
            });
            const grass = new THREE.Mesh(grassGeometry, grassMaterial);
            grass.rotation.x = -Math.PI / 2;
            grass.position.set(pos[0], 0.01, pos[2]);
            grass.receiveShadow = true;
            this.scene.add(grass);
        });
    }
    
    createRoadNetwork() {
        const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x2C2C2C });

        // Ana cadde (yatay)
        this.createRoad(-this.citySize/2, this.citySize/2, 0, 0, this.roadWidth, this.citySize, roadMaterial);

        // Ana cadde (dikey)
        this.createRoad(0, 0, -this.citySize/2, this.citySize/2, this.citySize, this.roadWidth, roadMaterial);

        // Yan sokaklar
        for (let i = -3; i <= 3; i++) {
            if (i !== 0) {
                // Yatay sokaklar
                this.createRoad(-this.citySize/2, this.citySize/2, i * this.blockSize, i * this.blockSize,
                              this.roadWidth/2, this.citySize, roadMaterial);

                // Dikey sokaklar
                this.createRoad(i * this.blockSize, i * this.blockSize, -this.citySize/2, this.citySize/2,
                              this.citySize, this.roadWidth/2, roadMaterial);
            }
        }

        // Yol çizgileri ve işaretleri
        this.createRoadMarkings();
    }

    createRoad(x1, x2, z1, z2, width, length, material) {
        const roadGeometry = new THREE.PlaneGeometry(width, length);
        const road = new THREE.Mesh(roadGeometry, material);
        road.rotation.x = -Math.PI / 2;
        road.position.set((x1 + x2) / 2, 0.01, (z1 + z2) / 2);
        road.receiveShadow = true;
        this.roads.push(road);
        this.scene.add(road);
    }
    
    createRoadMarkings() {
        const lineMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });

        // Ana cadde çizgileri
        for (let i = -this.citySize/2; i <= this.citySize/2; i += 10) {
            // Yatay çizgiler
            const hLineGeometry = new THREE.PlaneGeometry(4, 0.3);
            const hLine = new THREE.Mesh(hLineGeometry, lineMaterial);
            hLine.rotation.x = -Math.PI / 2;
            hLine.position.set(i, 0.02, 0);
            this.scene.add(hLine);

            // Dikey çizgiler
            const vLineGeometry = new THREE.PlaneGeometry(0.3, 4);
            const vLine = new THREE.Mesh(vLineGeometry, lineMaterial);
            vLine.rotation.x = -Math.PI / 2;
            vLine.position.set(0, 0.02, i);
            this.scene.add(vLine);
        }

        // Kavşak işaretleri
        this.createIntersectionMarkings();
    }

    createIntersectionMarkings() {
        const intersections = [
            [0, 0], // Merkez kavşak
            [-this.blockSize, 0], [this.blockSize, 0],
            [0, -this.blockSize], [0, this.blockSize]
        ];

        intersections.forEach(pos => {
            // Dur çizgisi
            const stopLineGeometry = new THREE.PlaneGeometry(this.roadWidth, 0.5);
            const stopLineMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
            const stopLine = new THREE.Mesh(stopLineGeometry, stopLineMaterial);
            stopLine.rotation.x = -Math.PI / 2;
            stopLine.position.set(pos[0], 0.02, pos[1] + this.roadWidth/2);
            this.scene.add(stopLine);
        });
    }
    
    createCityBlocks() {
        // Şehir bloklarını grid sisteminde oluştur
        for (let x = -3; x <= 3; x++) {
            for (let z = -3; z <= 3; z++) {
                if (x !== 0 && z !== 0) { // Ana caddeleri boş bırak
                    this.createCityBlock(x * this.blockSize, z * this.blockSize);
                }
            }
        }
    }

    createCityBlock(centerX, centerZ) {
        const buildingsPerBlock = 4 + Math.floor(Math.random() * 4); // 4-7 bina per blok

        for (let i = 0; i < buildingsPerBlock; i++) {
            const offsetX = (Math.random() - 0.5) * (this.blockSize - 10);
            const offsetZ = (Math.random() - 0.5) * (this.blockSize - 10);

            const x = centerX + offsetX;
            const z = centerZ + offsetZ;

            // Yollardan minimum mesafe kontrolü
            if (this.isValidBuildingPosition(x, z)) {
                this.createBuilding(x, 0, z, i);
            }
        }
    }

    isValidBuildingPosition(x, z) {
        const minDistanceFromRoad = 5;

        // Ana caddelere mesafe kontrolü
        if (Math.abs(x) < minDistanceFromRoad || Math.abs(z) < minDistanceFromRoad) {
            return false;
        }

        // Yan sokaklara mesafe kontrolü
        for (let i = -3; i <= 3; i++) {
            if (i !== 0) {
                if (Math.abs(x - i * this.blockSize) < minDistanceFromRoad ||
                    Math.abs(z - i * this.blockSize) < minDistanceFromRoad) {
                    return false;
                }
            }
        }

        return true;
    }
    
    createBuilding(x, y, z, index) {
        // Rastgele bina boyutları
        const width = 6 + Math.random() * 4;
        const height = 8 + Math.random() * 12;
        const depth = 6 + Math.random() * 4;
        
        // Bina renkleri
        const colors = [0x8B4513, 0xA0522D, 0xCD853F, 0xDEB887, 0xF4A460];
        const color = colors[index % colors.length];
        
        const buildingGeometry = new THREE.BoxGeometry(width, height, depth);
        const buildingMaterial = new THREE.MeshLambertMaterial({ color });
        const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
        
        building.position.set(x, height / 2, z);
        building.castShadow = true;
        building.receiveShadow = true;
        
        this.buildings.push(building);
        this.scene.add(building);
        
        // Pencereler ekle
        this.addWindows(building, width, height, depth);
    }
    
    addWindows(building, width, height, depth) {
        const windowMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x87CEEB,
            transparent: true,
            opacity: 0.7
        });
        
        // Ön yüz pencereleri
        const windowsPerRow = Math.floor(width / 2);
        const windowsPerCol = Math.floor(height / 3);
        
        for (let row = 0; row < windowsPerCol; row++) {
            for (let col = 0; col < windowsPerRow; col++) {
                const windowGeometry = new THREE.PlaneGeometry(0.8, 1.2);
                const window = new THREE.Mesh(windowGeometry, windowMaterial);
                
                window.position.set(
                    (col - windowsPerRow / 2 + 0.5) * 1.5,
                    (row - windowsPerCol / 2 + 0.5) * 2.5,
                    depth / 2 + 0.01
                );
                
                building.add(window);
            }
        }
    }
    
    createLandmarks() {
        // Akasya AVM (ana landmark)
        this.createAkasyaMall();

        // Hastane
        this.createHospital();

        // Okul
        this.createSchool();

        // Belediye binası
        this.createCityHall();
    }

    createAkasyaMall() {
        // Ana AVM binası
        const mallGeometry = new THREE.BoxGeometry(25, 15, 35);
        const mallMaterial = new THREE.MeshLambertMaterial({ color: 0x4169E1 });
        const mall = new THREE.Mesh(mallGeometry, mallMaterial);
        mall.position.set(60, 7.5, 60);
        mall.castShadow = true;
        mall.receiveShadow = true;
        this.buildings.push(mall);
        this.scene.add(mall);

        // AVM tabelası
        const signGeometry = new THREE.PlaneGeometry(15, 3);
        const signMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(60, 18, 42.5);
        this.scene.add(sign);

        // Otopark
        this.cityAssets.createParking(45, 0, 45, 20, 20);
    }

    createHospital() {
        const hospitalGeometry = new THREE.BoxGeometry(20, 12, 20);
        const hospitalMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF });
        const hospital = new THREE.Mesh(hospitalGeometry, hospitalMaterial);
        hospital.position.set(-60, 6, 60);
        hospital.castShadow = true;
        this.buildings.push(hospital);
        this.scene.add(hospital);

        // Kırmızı haç
        const crossGeometry = new THREE.BoxGeometry(2, 8, 1);
        const crossMaterial = new THREE.MeshLambertMaterial({ color: 0xFF0000 });
        const cross1 = new THREE.Mesh(crossGeometry, crossMaterial);
        cross1.position.set(-60, 10, 49.5);
        this.scene.add(cross1);

        const cross2 = new THREE.Mesh(new THREE.BoxGeometry(6, 2, 1), crossMaterial);
        cross2.position.set(-60, 10, 49.5);
        this.scene.add(cross2);
    }

    createSchool() {
        const schoolGeometry = new THREE.BoxGeometry(30, 8, 15);
        const schoolMaterial = new THREE.MeshLambertMaterial({ color: 0xDEB887 });
        const school = new THREE.Mesh(schoolGeometry, schoolMaterial);
        school.position.set(60, 4, -60);
        school.castShadow = true;
        this.buildings.push(school);
        this.scene.add(school);

        // Okul bahçesi
        this.cityAssets.createSchoolYard(60, -45);
    }

    createCityHall() {
        const cityHallGeometry = new THREE.BoxGeometry(18, 20, 18);
        const cityHallMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
        const cityHall = new THREE.Mesh(cityHallGeometry, cityHallMaterial);
        cityHall.position.set(-60, 10, -60);
        cityHall.castShadow = true;
        this.buildings.push(cityHall);
        this.scene.add(cityHall);

        // Türk bayrağı
        this.cityAssets.createFlag(-60, 25, -60);
    }

    createTraffic() {
        // Trafik ışıkları
        this.createTrafficLights();

        // Diğer araçlar (statik)
        this.createOtherVehicles();

        // Otobüs durakları
        this.createBusStops();
    }

    createTrafficLights() {
        const intersections = [
            [0, 0], // Merkez kavşak
            [-this.blockSize, 0], [this.blockSize, 0],
            [0, -this.blockSize], [0, this.blockSize]
        ];

        intersections.forEach(pos => {
            // Trafik ışığı direği
            const poleGeometry = new THREE.CylinderGeometry(0.2, 0.2, 8);
            const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.set(pos[0] + 5, 4, pos[1] + 5);
            pole.castShadow = true;
            this.scene.add(pole);

            // Işık kutusu
            const lightBoxGeometry = new THREE.BoxGeometry(1, 2, 0.5);
            const lightBoxMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const lightBox = new THREE.Mesh(lightBoxGeometry, lightBoxMaterial);
            lightBox.position.set(pos[0] + 5, 7, pos[1] + 5);
            this.scene.add(lightBox);

            // Işıklar (kırmızı, sarı, yeşil)
            this.createTrafficLightLamps(pos[0] + 5, 7, pos[1] + 5);
        });
    }

    createTrafficLightLamps(x, y, z) {
        const lampGeometry = new THREE.SphereGeometry(0.15, 8, 8);

        // Kırmızı ışık
        const redMaterial = new THREE.MeshLambertMaterial({
            color: 0xFF0000,
            emissive: 0x440000
        });
        const redLight = new THREE.Mesh(lampGeometry, redMaterial);
        redLight.position.set(x, y + 0.5, z);
        this.scene.add(redLight);

        // Sarı ışık
        const yellowMaterial = new THREE.MeshLambertMaterial({
            color: 0xFFFF00,
            emissive: 0x444400
        });
        const yellowLight = new THREE.Mesh(lampGeometry, yellowMaterial);
        yellowLight.position.set(x, y, z);
        this.scene.add(yellowLight);

        // Yeşil ışık
        const greenMaterial = new THREE.MeshLambertMaterial({
            color: 0x00FF00,
            emissive: 0x004400
        });
        const greenLight = new THREE.Mesh(lampGeometry, greenMaterial);
        greenLight.position.set(x, y - 0.5, z);
        this.scene.add(greenLight);
    }

    createOtherVehicles() {
        const vehiclePositions = [
            [20, 0, 2], [-30, 0, -2], [15, 0, 25], [-25, 0, -15],
            [35, 0, 0], [-40, 0, 0], [0, 0, 35], [0, 0, -40]
        ];

        vehiclePositions.forEach((pos, index) => {
            this.createRandomVehicle(pos[0], pos[1], pos[2], index);
        });
    }

    createRandomVehicle(x, y, z, index) {
        const vehicleTypes = ['car', 'truck', 'bus'];
        const type = vehicleTypes[index % vehicleTypes.length];

        let vehicle;

        switch(type) {
            case 'car':
                vehicle = this.cityAssets.createCar(x, y, z);
                break;
            case 'truck':
                vehicle = this.cityAssets.createTruck(x, y, z);
                break;
            case 'bus':
                vehicle = this.cityAssets.createBus(x, y, z);
                break;
        }

        if (vehicle) {
            vehicle.rotation.y = Math.random() * Math.PI * 2;
            this.vehicles.push(vehicle);
        }
    }

    createEnvironment() {
        // Ağaçlar
        this.createTrees();

        // Sokak lambaları
        this.createStreetLights();

        // Tabelalar
        this.createSigns();

        // Çöp kutuları
        this.createTrashCans();

        // Banklar
        this.createBenches();
    }

    createBusStops() {
        const busStopPositions = [
            [25, 5], [-25, 5], [25, -5], [-25, -5],
            [5, 25], [5, -25], [-5, 25], [-5, -25]
        ];

        busStopPositions.forEach(pos => {
            this.cityAssets.createBusStop(pos[0], pos[1]);
        });
    }

    createTrashCans() {
        const trashCanPositions = [
            [10, 10], [-10, 10], [10, -10], [-10, -10],
            [30, 0], [-30, 0], [0, 30], [0, -30],
            [15, 25], [-15, 25], [15, -25], [-15, -25]
        ];

        trashCanPositions.forEach(pos => {
            this.cityAssets.createTrashCan(pos[0], pos[1]);
        });
    }

    createBenches() {
        const benchPositions = [
            [12, 8], [-12, 8], [12, -8], [-12, -8],
            [8, 12], [-8, 12], [8, -12], [-8, -12]
        ];

        benchPositions.forEach(pos => {
            this.cityAssets.createBench(pos[0], pos[1]);
        });
    }
    
    createTrees() {
        const treePositions = [
            [15, 0, 15], [-15, 0, 15], [15, 0, -15], [-15, 0, -15],
            [35, 0, 35], [-35, 0, 35], [35, 0, -35], [-35, 0, -35]
        ];
        
        treePositions.forEach(pos => {
            // Gövde
            const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 3);
            const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 });
            const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
            trunk.position.set(pos[0], 1.5, pos[2]);
            trunk.castShadow = true;
            this.scene.add(trunk);
            
            // Yapraklar
            const leavesGeometry = new THREE.SphereGeometry(2, 8, 8);
            const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228B22 });
            const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
            leaves.position.set(pos[0], 4, pos[2]);
            leaves.castShadow = true;
            this.scene.add(leaves);
        });
    }
    
    createStreetLights() {
        const lightPositions = [
            [8, 0, 8], [-8, 0, 8], [8, 0, -8], [-8, 0, -8]
        ];
        
        lightPositions.forEach(pos => {
            // Direk
            const poleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 6);
            const poleMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
            const pole = new THREE.Mesh(poleGeometry, poleMaterial);
            pole.position.set(pos[0], 3, pos[2]);
            pole.castShadow = true;
            this.scene.add(pole);
            
            // Lamba
            const lampGeometry = new THREE.SphereGeometry(0.3, 8, 8);
            const lampMaterial = new THREE.MeshLambertMaterial({ 
                color: 0xFFFFAA,
                emissive: 0x444400
            });
            const lamp = new THREE.Mesh(lampGeometry, lampMaterial);
            lamp.position.set(pos[0], 6, pos[2]);
            this.scene.add(lamp);
            
            // Işık kaynağı
            const light = new THREE.PointLight(0xFFFFAA, 0.5, 20);
            light.position.set(pos[0], 6, pos[2]);
            light.castShadow = true;
            this.scene.add(light);
        });
    }
    
    createSigns() {
        // "Akasya Durağı" tabelası
        const signGeometry = new THREE.PlaneGeometry(4, 1);
        const signMaterial = new THREE.MeshLambertMaterial({ color: 0x0066CC });
        const sign = new THREE.Mesh(signGeometry, signMaterial);
        sign.position.set(0, 4, -10);
        sign.castShadow = true;
        this.scene.add(sign);
        
        // Taksi durağı işareti
        const taxiSignGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.1);
        const taxiSignMaterial = new THREE.MeshLambertMaterial({ color: 0xFFD700 });
        const taxiSign = new THREE.Mesh(taxiSignGeometry, taxiSignMaterial);
        taxiSign.position.set(5, 2, 5);
        taxiSign.rotation.x = Math.PI / 2;
        this.scene.add(taxiSign);
    }
    
    createSkybox() {
        // Basit gradient skybox
        const skyGeometry = new THREE.SphereGeometry(200, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({ 
            color: 0x87CEEB,
            side: THREE.BackSide
        });
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }
    
    // Çarpışma kontrolü
    checkCollision(position, radius = 1) {
        // Binalarla çarpışma kontrolü
        for (let building of this.buildings) {
            const distance = position.distanceTo(building.position);
            if (distance < radius + 3) { // Bina yarıçapı yaklaşık 3
                return true;
            }
        }
        return false;
    }
    
    // Rastgele güvenli pozisyon
    getRandomSafePosition() {
        let position;
        let attempts = 0;
        
        do {
            position = new THREE.Vector3(
                (Math.random() - 0.5) * 80,
                0,
                (Math.random() - 0.5) * 80
            );
            attempts++;
        } while (this.checkCollision(position) && attempts < 50);
        
        return position;
    }
}
